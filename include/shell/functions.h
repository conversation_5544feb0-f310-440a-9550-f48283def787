/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   functions.h                                        :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <rperez-tstudent.s19.be>          +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/04 00:00:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/04 13:44:13 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef KERNEL_FUNCTIONS_H
# define KERNEL_FUNCTIONS_H

#include "../structs.h"
#include "../enums.h"

/* ──────────── Terminal Management Functions ──────────── */
void		terminal_writestring(const char* data);
void		terminal_putchar(char c);

/* ──────────── Keyboard Functions ──────────── */
void		update_cursor(int scancode);
void		set_idt_gate(int n, uint32_t handler);
void		keyboard_handler();

/* ──────────── String Utility Functions ──────────── */
int			ft_strcmp(const char *s1, const char *s2);
char		*ft_strcpy(char *dest, const char *src);

/* ──────────── VGA and Hardware Functions ──────────── */
uint8_t		vga_entry_color(enum vga_color fg, enum vga_color bg);
uint16_t	vga_entry(unsigned char uc, uint8_t color);
void		vga_set_cursor(size_t row, size_t col);
void		outb(uint16_t port, uint8_t val);
void		outw(uint16_t port, uint16_t val);
uint8_t		inb(uint16_t port);
void		vga_cursor_restore();

/* ──────────── Number Printing Functions ──────────── */
void		printnbr(int n, int base);
void		printnbr_hex(uint32_t n);
void		printnbr_bin(uint32_t n);

/* ──────────── Shell Functions ──────────── */
void		shell_initialize();
void		shell_process_command(const char* cmd);
void		shell_handle_input(char c);
int			strcmp(const char* s1, const char* s2);
void		handle_memstats();
void		handle_memtest();
void		handle_vmemstats();
void		handle_memcheck();

/* ──────────── Kernel Core Functions ──────────── */
void		kernelPanic();

/* ──────────── GDT Functions ──────────── */
void		gdt_install();
void		gdt_set_gate(int num, uint32_t base, uint32_t limit, uint8_t access, uint8_t gran);
void		gdt_flush(void *);

/* ──────────── Stack Functions ──────────── */
void		stack_push(uint32_t value);
uint32_t	stack_pop();
uint32_t	stack_peek();
int			stack_is_empty();
void		print_kernel_stack();

/* ──────────── Additional Functions ──────────── */
void		crash_me();
size_t		strlen(const char* str);
void		terminal_putentryat(char c, uint8_t color, size_t x, size_t y);
void		terminal_write(const char* data, size_t size);

/* ──────────── Memory Utility Functions (C implementations) ──────────── */
void		*ft_memset(void *s, int c, size_t n);
void		*ft_memcpy(void *dst, const void *src, size_t n);
void		*ft_memmove(void *dst, const void *src, size_t n);
void		*ft_memchr(const void *s, int c, size_t n);
void		ft_bzero(void *s, size_t n);
int			ft_memcmp(const void *s1, const void *s2, size_t n);

#endif /* KERNEL_FUNCTIONS_H */
