/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   statics.h                                          :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/04 00:00:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/04 00:00:00 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef STATICS_H
# define STATICS_H

#include "structs.h"

/* ──────────── Static Memory Variables (Declarations) ──────────── */

/* Physical memory bitmap storage */
/* static uint8_t phys_bitmap[BITMAP_SIZE]; - defined in init.c */

/* Kernel page directory storage */
/* static t_page_directory kernel_page_directory __attribute__((aligned(4096))); - defined in init.c */

/* Virtual memory area list */
/* static t_vma *vma_list = NULL; - defined in allocation.c */
/* static uint32_t next_virt_addr = USER_SPACE_START; - defined in allocation.c */

/* ──────────── Static Function Declarations ──────────── */

/* Note: Static functions are not declared in headers as they are internal to their compilation units.
 * This file serves as documentation of static variables used across the codebase.
 * 
 * Static variables are defined in their respective source files:
 * - Physical memory bitmap: src/memory/init.c
 * - Kernel page directory: src/memory/init.c  
 * - VMA management: src/memory/allocation.c
 * - Helper functions: various source files
 */

#endif /* STATICS_H */
