/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   globals.h                                          :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <rperez-tstudent.s19.be>          +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/04 00:00:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/04 13:38:56 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef GLOBALS_H
# define GLOBALS_H

#include "structs.h"

/* ──────────── Global <PERSON> ──────────── */
extern t_kernel	kernel;

/* ──────────── Global Memory Management Variables ──────────── */
extern t_phys_mem_manager g_phys_mem_manager;
extern t_page_directory *g_kernel_directory;
extern t_page_directory *g_current_directory;
extern t_kernel_heap g_kernel_heap;

/* ──────────── Global Virtual Memory Variables ──────────── */
/* Note: vma_list is static in vmalloc.c */

#endif /* GLOBALS_H */
