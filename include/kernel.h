/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   kernel.h                                           :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <rperez-tstudent.s19.be>          +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/06/05 11:48:23 by alexafer          #+#    #+#             */
/*   Updated: 2025/07/04 13:44:23 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef KERNEL_H
# define KERNEL_H

/* ──────────── Master Include System ──────────── */
/* Import all organized headers in dependency order */

#include "libs.h"           /* Standard C libraries and cross-compiler validation */
#include "defines.h"        /* All constants and defines */
#include "enums.h"          /* All enumerations */
#include "structs.h"        /* All structure definitions */
#include "globals.h"        /* All global variables */
#include "statics.h"        /* Static variables documentation */

/* Function declarations by module */
#include "memory/functions.h"   /* Memory management functions */
#include "idt/functions.h"      /* IDT and interrupt functions */
#include "shell/functions.h"   /* Kernel core functions */

/* ──────────── Master Include System ──────────── */
void		terminal_offset(uint16_t offset);
void		terminal_restore();
void		terminal_initialize();

#endif /* KERNEL_H */
