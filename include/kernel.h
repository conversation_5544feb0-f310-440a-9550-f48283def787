/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   kernel.h                                           :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <rperez-tstudent.s19.be>          +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/06/05 11:48:23 by alexafer          #+#    #+#             */
/*   Updated: 2025/07/04 14:33:09 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef KERNEL_H
# define KERNEL_H

/* ──────────── Master Include System ──────────── */
/* Import all organized headers in dependency order */

#include "libs.h"           /* Standard C libraries and cross-compiler validation */
#include "defines.h"        /* All constants and defines */
#include "enums.h"          /* All enumerations */
#include "structs.h"        /* All structure definitions */
#include "globals.h"        /* All global variables */

/* Function declarations by module - matching src structure */
#include "gdt/functions.h"      /* GDT and stack functions */
#include "interupts/functions.h" /* Interrupt management functions */
#include "memory/functions.h"   /* Memory management functions */
#include "shell/functions.h"    /* Shell and terminal functions */
#include "kernel/functions.h"   /* Kernel core functions */

#endif /* KERNEL_H */
