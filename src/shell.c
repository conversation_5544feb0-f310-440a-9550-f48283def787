/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   shell.c                                            :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/06/17 21:58:20 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/01 20:24:52 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../include/kernel.h"

#define COMMAND_BUFFER_SIZE 256
static char command_buffer[COMMAND_BUFFER_SIZE];
static int buffer_pos = 0;

typedef enum {
    CMD_HELP,
    CMD_STACK,
    CMD_PUSH,
    CMD_POP,
    CMD_CLEAR,
    CMD_REBOOT,
    CMD_HALT,
    CMD_SHUTDOWN,
    CMD_MEMSTATS,
    CMD_MEMTEST,
    CMD_VMEMSTATS,
    CMD_MEMCHECK,
    CMD_UNKNOWN
} command_type_t;

void shell_initialize() {
    terminal_writestring("KFS Shell v1.0\n");
    terminal_writestring("Type 'help' for available commands\n");
    terminal_writestring("> ");
}

void handle_help() {
    terminal_writestring("Available commands:\n");
    terminal_writestring("  help         - Display this help message\n");
    terminal_writestring("  stack        - Print the kernel stack\n");
    terminal_writestring("  push <hex>   - Push a value onto the stack\n");
    terminal_writestring("  pop          - Pop a value from the stack\n");
    terminal_writestring("  clear        - Clear the screen\n");
    terminal_writestring("  reboot       - Reboot the system\n");
    terminal_writestring("  halt         - Halt the system\n");
    terminal_writestring("  shutdown     - Shutdown the system\n");
    terminal_writestring("\nMemory Management Commands:\n");
    terminal_writestring("  memstats     - Display memory statistics\n");
    terminal_writestring("  memtest      - Run memory allocation tests\n");
    terminal_writestring("  vmemstats    - Display virtual memory statistics\n");
    terminal_writestring("  memcheck     - Check memory integrity\n");
}

void handle_stack() {
    print_kernel_stack();
}

void handle_push(const char* arg) {
    if (arg[0] == '\0') {
        terminal_writestring("Error: push requires a hexadecimal value\n");
        return;
    }

    uint32_t value = 0;
    int i = 0;

    if (arg[0] == '0' && (arg[1] == 'x' || arg[1] == 'X'))
        i = 2;

    while (arg[i] != '\0') {
        value = value * 16;

        if (arg[i] >= '0' && arg[i] <= '9')
            value += arg[i] - '0';
        else if (arg[i] >= 'a' && arg[i] <= 'f')
            value += arg[i] - 'a' + 10;
        else if (arg[i] >= 'A' && arg[i] <= 'F')
            value += arg[i] - 'A' + 10;
        else {
            terminal_writestring("Error: Invalid hex value\n");
            return;
        }
        i++;
    }

    if (value != 0 || (arg[0] == '0' && arg[1] == '\0')) {
        stack_push(value);
        terminal_writestring("Pushed 0x");

        for (int j = 7; j >= 0; j--) {
            uint8_t nibble = (value >> (j * 4)) & 0xF;
            char hex_char;
            if (nibble < 10)
                hex_char = '0' + nibble;
            else
                hex_char = 'A' + (nibble - 10);
            terminal_putchar(hex_char);
        }

        terminal_writestring(" onto the stack\n");
    }
}

void handle_pop() {
    if (stack_is_empty()) {
        terminal_writestring("Error: Stack is empty\n");
        return;
    }

    uint32_t value = stack_pop();
    terminal_writestring("Popped 0x");

    for (int j = 7; j >= 0; j--) {
        uint8_t nibble = (value >> (j * 4)) & 0xF;
        char hex_char;
        if (nibble < 10)
            hex_char = '0' + nibble;
        else
            hex_char = 'A' + (nibble - 10);
        terminal_putchar(hex_char);
    }

    terminal_writestring(" from the stack\n");
}

void handle_clear() {
    terminal_initialize();
}

void handle_reboot() {
    terminal_writestring("Rebooting...\n");
    outb(0x64, 0xFE);
}

void handle_halt() {
    terminal_writestring("System halted\n");
    __asm__ volatile("hlt");
}

void handle_shutdown() {
    terminal_writestring("Shutting down...\n");

    outw(0xB004, 0x2000); /* ACPI shutdown */
    outw(0x604, 0x2000);  /* APM shutdown */

    terminal_writestring("Shutdown failed, halting CPU\n");
    __asm__ volatile("cli; hlt");
}

void handle_unknown(const char* command) {
    terminal_writestring("Unknown command: ");
    terminal_writestring(command);
    terminal_writestring("\n");
}

command_type_t get_command_type(const char* command) {
	command_type_t cmd_type;

	cmd_type = CMD_UNKNOWN;
    if (strcmp(command, "help") == 0)
		cmd_type = CMD_HELP;
    else if (strcmp(command, "stack") == 0)
		cmd_type = CMD_STACK;
    else if (strcmp(command, "push") == 0)
		cmd_type = CMD_PUSH;
    else if (strcmp(command, "pop") == 0)
		cmd_type = CMD_POP;
    else if (strcmp(command, "clear") == 0)
		cmd_type = CMD_CLEAR;
    else if (strcmp(command, "reboot") == 0)
		cmd_type = CMD_REBOOT;
    else if (strcmp(command, "halt") == 0)
		cmd_type = CMD_HALT;
    else if (strcmp(command, "shutdown") == 0)
		cmd_type = CMD_SHUTDOWN;
    else if (strcmp(command, "memstats") == 0)
		cmd_type = CMD_MEMSTATS;
    else if (strcmp(command, "memtest") == 0)
		cmd_type = CMD_MEMTEST;
    else if (strcmp(command, "vmemstats") == 0)
		cmd_type = CMD_VMEMSTATS;
    else if (strcmp(command, "memcheck") == 0)
		cmd_type = CMD_MEMCHECK;
	return cmd_type;
}

void shell_process_command(const char* cmd) {
    char command[32] = {0};
    char arg[32] = {0};
    int i = 0, j = 0;

    while (cmd[i] && cmd[i] != ' ' && i < 31) {
        command[i] = cmd[i];
        i++;
    }
    command[i] = '\0';

    while (cmd[i] && cmd[i] == ' ')
        i++;

    while (cmd[i] && j < 31) {
        arg[j] = cmd[i];
        i++;
        j++;
    }
    arg[j] = '\0';

    command_type_t cmd_type = get_command_type(command);

    switch (cmd_type) {
        case CMD_HELP:
            handle_help();
            break;
        case CMD_STACK:
            handle_stack();
            break;
        case CMD_PUSH:
            handle_push(arg);
            break;
        case CMD_POP:
            handle_pop();
            break;
        case CMD_CLEAR:
            handle_clear();
            break;
        case CMD_REBOOT:
            handle_reboot();
            break;
        case CMD_HALT:
            handle_halt();
            break;
        case CMD_SHUTDOWN:
            handle_shutdown();
            break;
        case CMD_MEMSTATS:
            handle_memstats();
            break;
        case CMD_MEMTEST:
            handle_memtest();
            break;
        case CMD_VMEMSTATS:
            handle_vmemstats();
            break;
        case CMD_MEMCHECK:
            handle_memcheck();
            break;
        case CMD_UNKNOWN:
            if (command[0] != '\0')
                handle_unknown(command);
            break;
    }

    terminal_writestring("> ");
}

void shell_handle_input(char c) {
    if (c == '\n') {
        terminal_putchar('\n');
        command_buffer[buffer_pos] = '\0';
        shell_process_command(command_buffer);
        buffer_pos = 0;
    } else if (c == '\b' && buffer_pos > 0) {
        buffer_pos--;
        terminal_putchar('\b');
        terminal_putchar(' ');
        terminal_putchar('\b');
    } else if (c >= ' ' && c <= '~' && buffer_pos < COMMAND_BUFFER_SIZE - 1) {
        command_buffer[buffer_pos++] = c;
        terminal_putchar(c);
    }
}

int strcmp(const char* s1, const char* s2) {
    while (*s1 && (*s1 == *s2)) {
        s1++;
        s2++;
    }
    return *(const unsigned char*)s1 - *(const unsigned char*)s2;
}

/* Memory Management Command Handlers */

void handle_memstats() {
    terminal_writestring("\n=== Physical Memory Statistics ===\n");
    terminal_writestring("Total pages: ");
    printnbr(g_phys_mem_manager.total_pages, 10);
    terminal_writestring("\nUsed pages: ");
    printnbr(g_phys_mem_manager.used_pages, 10);
    terminal_writestring("\nFree pages: ");
    printnbr(g_phys_mem_manager.total_pages - g_phys_mem_manager.used_pages, 10);
    terminal_writestring("\n\n=== Kernel Heap Statistics ===\n");
    terminal_writestring("Heap start: 0x");
    printnbr(g_kernel_heap.start_addr, 16);
    terminal_writestring("\nHeap end: 0x");
    printnbr(g_kernel_heap.current_end, 16);
    terminal_writestring("\nHeap size: ");
    printnbr((g_kernel_heap.current_end - g_kernel_heap.start_addr) / 1024, 10);
    terminal_writestring(" KB\n");

    if (is_paging_enabled()) {
        terminal_writestring("\nPaging: ENABLED\n");
        terminal_writestring("Page directory: 0x");
        printnbr(get_cr3(), 16);
        terminal_writestring("\n");
    } else {
        terminal_writestring("\nPaging: DISABLED\n");
    }
}

void handle_memtest() {
    void *ptrs[20];
    void *large_ptrs[5];
    void *vptrs[10];
    int i;
    uint32_t initial_used_pages, final_used_pages;

    terminal_writestring("\n=== Comprehensive Memory Test Suite ===\n");

    /* Record initial memory state */
    initial_used_pages = g_phys_mem_manager.used_pages;
    terminal_writestring("Initial used pages: ");
    printnbr(initial_used_pages, 10);
    terminal_writestring("\n\n");

    /* ========== TEST 1: Basic Allocation Tests ========== */
    terminal_writestring("TEST 1: Basic Allocation Tests\n");
    terminal_writestring("----------------------------------\n");

    /* Test various sizes */
    uint32_t test_sizes[] = {16, 32, 64, 128, 256, 512, 1024, 2048, 4096};
    uint32_t num_sizes = sizeof(test_sizes) / sizeof(test_sizes[0]);

    for (i = 0; i < (int)num_sizes; i++) {
        void *ptr = kmalloc(test_sizes[i]);
        if (ptr) {
            terminal_writestring("kmalloc(");
            printnbr(test_sizes[i], 10);
            terminal_writestring("): SUCCESS - Size: ");
            printnbr(ksize(ptr), 10);
            terminal_writestring(" bytes\n");

            /* Write pattern to memory to test accessibility */
            uint32_t *data = (uint32_t *)ptr;
            uint32_t pattern = 0xDEADBEEF + i;
            *data = pattern;

            /* Verify pattern */
            if (*data == pattern) {
                terminal_writestring("  Memory write/read: OK\n");
            } else {
                terminal_writestring("  Memory write/read: FAILED\n");
            }

            kfree(ptr);
        } else {
            terminal_writestring("kmalloc(");
            printnbr(test_sizes[i], 10);
            terminal_writestring("): FAILED\n");
        }
    }

    /* ========== TEST 2: Multiple Allocations ========== */
    terminal_writestring("\nTEST 2: Multiple Small Allocations\n");
    terminal_writestring("------------------------------------\n");

    /* Allocate multiple small blocks */
    int successful_allocs = 0;
    for (i = 0; i < 20; i++) {
        ptrs[i] = kmalloc(128);
        if (ptrs[i]) {
            successful_allocs++;
            /* Write unique pattern to each block */
            uint32_t *data = (uint32_t *)ptrs[i];
            *data = 0xCAFEBABE + i;
        }
    }

    terminal_writestring("Allocated ");
    printnbr(successful_allocs, 10);
    terminal_writestring("/20 blocks\n");

    /* Verify all patterns */
    int pattern_errors = 0;
    for (i = 0; i < successful_allocs; i++) {
        if (ptrs[i]) {
            uint32_t *data = (uint32_t *)ptrs[i];
            if (*data != (0xCAFEBABE + i)) {
                pattern_errors++;
            }
        }
    }

    terminal_writestring("Pattern verification: ");
    if (pattern_errors == 0) {
        terminal_writestring("PASSED\n");
    } else {
        terminal_writestring("FAILED (");
        printnbr(pattern_errors, 10);
        terminal_writestring(" errors)\n");
    }

    /* Free every other block (fragmentation test) */
    for (i = 0; i < successful_allocs; i += 2) {
        if (ptrs[i]) {
            kfree(ptrs[i]);
            ptrs[i] = NULL;
        }
    }

    /* Allocate new blocks in fragmented space */
    int refill_count = 0;
    for (i = 0; i < successful_allocs; i += 2) {
        ptrs[i] = kmalloc(64); /* Smaller size to fit in gaps */
        if (ptrs[i]) refill_count++;
    }

    terminal_writestring("Fragmentation test: Refilled ");
    printnbr(refill_count, 10);
    terminal_writestring(" gaps\n");

    /* Clean up remaining allocations */
    for (i = 0; i < successful_allocs; i++) {
        if (ptrs[i]) {
            kfree(ptrs[i]);
            ptrs[i] = NULL;
        }
    }

    /* ========== TEST 3: Large Allocations ========== */
    terminal_writestring("\nTEST 3: Large Allocations\n");
    terminal_writestring("---------------------------\n");

    uint32_t large_sizes[] = {8192, 16384, 32768, 65536, 131072};
    uint32_t num_large = sizeof(large_sizes) / sizeof(large_sizes[0]);

    for (i = 0; i < (int)num_large && i < 5; i++) {
        large_ptrs[i] = kmalloc(large_sizes[i]);
        if (large_ptrs[i]) {
            terminal_writestring("Large alloc ");
            printnbr(large_sizes[i] / 1024, 10);
            terminal_writestring("KB: SUCCESS\n");

            /* Test memory boundaries */
            uint32_t *start = (uint32_t *)large_ptrs[i];
            uint32_t *end = (uint32_t *)((uint8_t *)large_ptrs[i] + large_sizes[i] - 4);

            *start = 0x12345678;
            *end = 0x87654321;

            if (*start == 0x12345678 && *end == 0x87654321) {
                terminal_writestring("  Boundary test: PASSED\n");
            } else {
                terminal_writestring("  Boundary test: FAILED\n");
            }
        } else {
            terminal_writestring("Large alloc ");
            printnbr(large_sizes[i] / 1024, 10);
            terminal_writestring("KB: FAILED\n");
            large_ptrs[i] = NULL;
        }
    }

    /* Clean up large allocations */
    for (i = 0; i < 5; i++) {
        if (large_ptrs[i]) {
            kfree(large_ptrs[i]);
        }
    }

    /* ========== TEST 4: Virtual Memory Tests ========== */
    terminal_writestring("\nTEST 4: Virtual Memory Tests\n");
    terminal_writestring("-----------------------------\n");

    /* Test vmalloc allocations */
    int vmalloc_success = 0;
    for (i = 0; i < 10; i++) {
        vptrs[i] = vmalloc(4096); /* One page each */
        if (vptrs[i]) {
            vmalloc_success++;

            /* Test virtual memory access */
            uint32_t *vdata = (uint32_t *)vptrs[i];
            *vdata = 0xFEEDFACE + i;

            if (*vdata == (0xFEEDFACE + i)) {
                /* Success - memory is accessible */
            } else {
                terminal_writestring("Virtual memory access failed\n");
            }
        }
    }

    terminal_writestring("vmalloc: ");
    printnbr(vmalloc_success, 10);
    terminal_writestring("/10 successful\n");

    /* Test vsize */
    if (vmalloc_success > 0 && vptrs[0]) {
        terminal_writestring("vsize test: ");
        printnbr(vsize(vptrs[0]), 10);
        terminal_writestring(" bytes\n");
    }

    /* Clean up virtual allocations */
    for (i = 0; i < 10; i++) {
        if (vptrs[i]) {
            vfree(vptrs[i]);
        }
    }

    /* ========== TEST 5: Edge Cases ========== */
    terminal_writestring("\nTEST 5: Edge Cases\n");
    terminal_writestring("-------------------\n");

    /* Test zero allocation */
    void *zero_ptr = kmalloc(0);
    terminal_writestring("kmalloc(0): ");
    if (zero_ptr == NULL) {
        terminal_writestring("Correctly returned NULL\n");
    } else {
        terminal_writestring("ERROR: Returned non-NULL\n");
        kfree(zero_ptr);
    }

    /* Test double free protection */
    void *double_free_ptr = kmalloc(256);
    if (double_free_ptr) {
        kfree(double_free_ptr);
        terminal_writestring("Double free test: ");
        kfree(double_free_ptr); /* This should trigger a warning */
        terminal_writestring("Protection active\n");
    }

    /* Test invalid free */
    terminal_writestring("Invalid free test: ");
    kfree((void *)0x12345678); /* This should trigger a warning */
    terminal_writestring("Protection active\n");

    /* ========== TEST 6: Memory Leak Check ========== */
    terminal_writestring("\nTEST 6: Memory Leak Check\n");
    terminal_writestring("--------------------------\n");

    final_used_pages = g_phys_mem_manager.used_pages;
    terminal_writestring("Final used pages: ");
    printnbr(final_used_pages, 10);
    terminal_writestring("\nPage difference: ");
    if (final_used_pages >= initial_used_pages) {
        printnbr(final_used_pages - initial_used_pages, 10);
        terminal_writestring(" (normal - heap expansion)\n");
    } else {
        printnbr(initial_used_pages - final_used_pages, 10);
        terminal_writestring(" (unexpected decrease)\n");
    }

    /* ========== FINAL RESULTS ========== */
    terminal_writestring("\n=== Test Suite Complete ===\n");
    terminal_writestring("All major memory management features tested\n");
    terminal_writestring("Check above for any FAILED tests\n");
    terminal_writestring("Memory system appears functional\n");
}

void handle_vmemstats() {
    vmem_print_stats();
}

void handle_memcheck() {
    terminal_writestring("\n=== Memory Integrity Check ===\n");

    /* Check if memory management is initialized */
    if (g_phys_mem_manager.total_pages == 0) {
        terminal_writestring("ERROR: Physical memory manager not initialized\n");
        return;
    }

    if (g_kernel_heap.start_addr == 0) {
        terminal_writestring("ERROR: Kernel heap not initialized\n");
        return;
    }

    if (!g_current_directory) {
        terminal_writestring("ERROR: Page directory not set\n");
        return;
    }

    terminal_writestring("Physical memory manager: OK\n");
    terminal_writestring("Kernel heap: OK\n");
    terminal_writestring("Paging system: OK\n");

    /* Check for memory leaks by comparing allocated vs used pages */
    uint32_t used_pages = g_phys_mem_manager.used_pages;
    uint32_t expected_pages = (g_kernel_heap.current_end - g_kernel_heap.start_addr) / PAGE_SIZE;
    expected_pages += 1; /* Page directory */

    terminal_writestring("Used pages: ");
    printnbr(used_pages, 10);
    terminal_writestring("\nExpected minimum: ");
    printnbr(expected_pages, 10);
    terminal_writestring("\n");

    if (used_pages >= expected_pages) {
        terminal_writestring("Memory usage: NORMAL\n");
    } else {
        terminal_writestring("WARNING: Unexpected memory usage pattern\n");
    }

    terminal_writestring("Memory integrity check completed\n");
}
